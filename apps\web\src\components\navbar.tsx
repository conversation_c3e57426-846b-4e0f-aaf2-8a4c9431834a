import { <PERSON> } from "@tanstack/react-router";
import { But<PERSON> } from "./ui/button";
import UserMenu from "./user-menu";

export default function Navbar() {
  return (
    <nav className="fixed top-0 left-0 right-0 p-4 z-50">
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex justify-center items-center cursor-target">
          <img src="/logo_white.png" alt="Logo" height={36} width={36} />
          <Link to="/" className="font-bold text-lg pr-1">&gt;ssh occo.rocks</Link>
        </div>
        <div className="flex items-center gap-4">
          <Button variant="ghost" asChild>
            <Link to="/about">About</Link>
          </Button>
          <Button variant="ghost" asChild>
            <Link to="/projects">Projects</Link>
          </Button>
          <Button variant="ghost" asChild>
            <Link to="/blog">Blog</Link>
          </Button>
          <UserMenu />
        </div>
      </div>
    </nav>
  );
}


