import type { DiscordApiResponse, DiscordStatusData } from "";
import { generateId } from "better-auth";
import { eq } from "drizzle-orm";
import z from "zod";
import { db } from "@/db";
import { blogs, message } from "@/db/schema";
import { protectedProcedure, publicProcedure } from "../lib/orpc";

export const appRouter = {
	sendMessage: publicProcedure
		.input(z.object({ content: z.string() }))
		.handler(async ({ input }) => {
			const { content } = input;

			const alreadySent = await db.query.message.findFirst({
				where: eq(message.content, content),
			});

			if (alreadySent) {
				return;
			}

			await db
				.insert(message)
				.values({
					id: generateId(),
					content,
				})
				.catch((err) => {
					console.error(err);
					return {
						error: "ERR_101",
						message: "Failed to send message",
					};
				});

			return {
				message: "Message sent",
			};
		}),
	getBlogs: publicProcedure.handler(async () => {
		const blogs = await db.query.blogs.findMany();

		return {
			blogs,
		};
	}),
	createBlog: protectedProcedure
		.input(
			z.object({
				slug: z.string(),
				title: z.string(),
				content: z.string(),
				icon: z.string(),
				color: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			const { slug, title, content, icon, color } = input;

			const existingBlog = await db.query.blogs.findFirst({
				where: eq(blogs.id, slug),
			});

			if (existingBlog) {
				throw new Error("Blog with this slug already exists");
			}

			await db
				.insert(blogs)
				.values({ id: slug, name: title, description: content, icon, color })
				.catch((err) => {
					console.error(err);
					return {
						error: "ERR_101",
						message: "Failed to create blog",
					};
				});
			return { success: true };
		}),
	discord: publicProcedure.handler(async () => {
		const data = await fetch(
			"https://************:4001/v1/users/1383592267201384469",
		);
		const json = (await data.json()) as DiscordApiResponse;

		const discordData: DiscordStatusData = {
			discord_user: {
				id: json.data.discord_user.id,
				display_name: json.data.discord_user.display_name,
				avatar: json.data.discord_user.avatar,
				primary_guild: json.data.discord_user.primary_guild
					? {
							tag: json.data.discord_user.primary_guild.tag,
						}
					: undefined,
			},
			discord_status: json.data.discord_status,
			activities: json.data.activities.map((activity) => ({
				type: activity.type,
				state: activity.state,
				details: activity.details,
			})),
		};

		return discordData;
	}),
};
export type AppRouter = typeof appRouter;
