{"name": "occo_rocks", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check --write .", "dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F server dev", "db:push": "turbo -F server db:push", "db:studio": "turbo -F server db:studio", "db:generate": "turbo -F server db:generate", "db:migrate": "turbo -F server db:migrate", "postinstall": "bun run apps/shared build && bun run app/server build"}, "dependencies": {}, "devDependencies": {"turbo": "^2.5.4", "@biomejs/biome": "^2.1.2"}, "packageManager": "bun@1.2.18"}