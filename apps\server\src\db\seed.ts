import { auth } from "@/lib/auth";
import { db } from "./index";

const seed = async () => {
	const account = await db.query.account.findFirst();
	const user = await db.query.user.findFirst();
	if (account || user) return console.log("Database already seeded");
	await auth.api
		.signUpEmail({
			body: {
				email: `${process.env.ROOT_EMAIL}`,
				password: `${process.env.ROOT_PASSWORD}`,
				name: `${process.env.ROOT_USERNAME}`,
			},
		})
		.catch((err) => {
			console.error(err);
		});
};

seed()
	.catch((err) => {
		console.error(err);
		process.exit(1);
	})
	.then(() => {
		console.log("Database seeded");
	})
	.finally(() => {
		process.exit(0);
	});
