export interface User {
    id: string;
    name: string;
    email: string;
    emailVerified: boolean;
    image?: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface Session {
    id: string;
    userId: string;
    expiresAt: Date;
    token: string;
    ipAddress?: string;
    userAgent?: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface AuthState {
    user: User | null;
    session: Session | null;
    isAuthenticated: boolean;
    isLoading: boolean;
}
export interface LoginCredentials {
    email: string;
    password: string;
}
export interface RegisterCredentials extends LoginCredentials {
    name: string;
    confirmPassword: string;
}
export interface AuthResponse {
    user: User;
    session: Session;
}
//# sourceMappingURL=auth.d.ts.map