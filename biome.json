{"$schema": "https://biomejs.dev/schemas/2.1.3/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**", "!**/.next", "!**/dist", "!**/.turbo", "!**/dev-dist", "!**/.zed", "!**/.vscode", "!**/routeTree.gen.ts", "!**/src-tauri", "!**/.nuxt", "!bts.jsonc", "!**/.expo", "!**/.wrangler", "!**/.source"]}, "formatter": {"enabled": true, "indentStyle": "tab"}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"useExhaustiveDependencies": "info"}, "nursery": {"useSortedClasses": {"level": "warn", "fix": "safe", "options": {"functions": ["clsx", "cva", "cn"]}}}, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}