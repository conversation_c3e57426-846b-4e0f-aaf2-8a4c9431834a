export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}
export interface ApiError {
    code: string;
    message: string;
    details?: Record<string, any>;
}
export type Status = "idle" | "loading" | "success" | "error";
export interface FormState<T = any> {
    data: T;
    errors: Record<string, string>;
    isSubmitting: boolean;
    isDirty: boolean;
}
//# sourceMappingURL=api.d.ts.map