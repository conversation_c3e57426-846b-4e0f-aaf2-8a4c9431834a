// import * as React from "react"

// import { cn } from "@/lib/utils"

// function Input({ className, type, ...props }: React.ComponentProps<"input">) {
//   return (
//     <input
//       type={type}
//       data-slot="input"
//       className={cn(
//         "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
//         "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
//         "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
//         className
//       )}
//       {...props}
//     />
//   )
// }

// export { Input }

import type { AnyFieldApi } from "@tanstack/react-form";
import type * as React from "react";
import { cn } from "@/lib/utils";

function Input({
  className,
  type,
  field,
  ...props
}: React.ComponentProps<"input"> & { field?: AnyFieldApi }) {
  return (
    <div className="flex flex-col gap-1">
      <input
        type={type}
        data-slot="input"
        className={cn(
          "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md bg-background px-3 py-1 text-base shadow-md transition-all outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:opacity-50 md:text-sm",
          // 3D effect like the button
          "border border-border/80 border-b-4 border-b-border/70",
          // Focus states with 3D preservation
          "focus-visible:border-ring/80 focus-visible:border-b-ring/60 focus-visible:ring-ring/50 focus-visible:ring-[3px]",
          // Invalid states with 3D preservation
          "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive/80 aria-invalid:border-b-destructive/70",
          // Active/pressed effect
          "active:border-b-2 active:translate-y-0.5 active:transition-all active:duration-150",
          className,
        )}
        {...props}
      />
      {field?.state.meta.isTouched && !field.state.meta.isValid ? (
        <p className="text-destructive text-sm">{field.state.meta.errors.join(", ")}</p>
      ) : null}
    </div>
  );
}

export { Input };