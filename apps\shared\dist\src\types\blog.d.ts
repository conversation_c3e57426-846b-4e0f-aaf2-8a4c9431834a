export interface Blog {
    id: string;
    name: string;
    description: string;
    icon: string;
    color: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface CreateBlogInput {
    slug: string;
    title: string;
    content: string;
    icon: string;
    color: string;
}
export interface UpdateBlogInput extends Partial<CreateBlogInput> {
    id: string;
}
export interface BlogsResponse {
    blogs: Blog[];
}
export interface Message {
    id: string;
    content: string;
    createdAt: Date;
}
export interface SendMessageInput {
    content: string;
}
export interface MessageResponse {
    message: string;
}
//# sourceMappingURL=blog.d.ts.map