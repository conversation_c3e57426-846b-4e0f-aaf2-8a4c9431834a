{"name": "@occo_rocks/shared", "version": "0.0.1", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./types/*": {"types": "./dist/types/*.d.ts", "import": "./dist/types/*.js"}}, "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "check-types": "tsc --noEmit"}, "devDependencies": {"typescript": "^5.8.3"}}