import { pgTable, text, timestamp } from "drizzle-orm/pg-core";
import { timestamps } from "@/db/column.helpers";

export const blogs = pgTable("blogs", {
	id: text("id").primaryKey(),
	name: text("name").notNull(),
	description: text("description").notNull(),
	icon: text("icon").notNull(),
	color: text("color").notNull(),
	...timestamps,
});

export const message = pgTable("message", {
	id: text("id").primaryKey(),
	content: text("content").notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});
