import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>G<PERSON><PERSON>, <PERSON><PERSON> } from "@icons-pack/react-simple-icons";
import { createFileRoute } from "@tanstack/react-router";
import { Mail } from "lucide-react";

import { DiscordStatus } from "@/components/discord-status";
import { Button } from "@/components/ui/button";
import { orpc } from "@/utils/orpc";

export const Route = createFileRoute("/about")({
  component: About,
  loader: async () => orpc.discord.queryOptions(),
});

function About() {
  const { data } = Route.useLoaderData()
  const now = new Date().toLocaleTimeString('en-GB', { timeZone: 'Europe/London', hour: '2-digit', minute: '2-digit' });
  return (
    <div className="min-h-screen bg-background text-white font-primary overflow-hidden relative w-full">

      <main className="container mx-auto px-4 pt-20 flex flex-col items-center justify-center min-h-screen">
        <div className="max-w-4xl mx-auto w-full">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold">
              about me
            </h1>
          </div>

          <div className="grid md:grid-cols-2 gap-12 items-start text-left">
            <div className="space-y-6">
              <p className="text-lg text-white/90 leading-relaxed">
                hey there! i'm occorune, a developer who enjoys building things
                that work well and look good. i spend most of my time writing
                code, experimenting with new tech, and occasionally touching
                grass.
              </p>

              <p className="text-lg text-white/75 leading-relaxed">
                when i'm not coding, you'll probably find me gaming, listening
                to music, or procrastinating on side projects that i swear i'll
                finish someday and if you can't find me, i'm sleeping.
              </p>

              <div className="pt-4">
                <h2 className="text-2xl font-semibold mb-4">
                  what i work with
                </h2>
                <div className="flex flex-wrap gap-2">
                  {[
                    "TypeScript",
                    "React",
                    "Node.js",
                    "Bun",
                    "Hono",
                    "Tailwind",
                    "PostgreSQL",
                  ].map((tech) => (
                    <span
                      key={tech}
                      className="px-3 py-1 bg-white/10 rounded-full text-sm border border-white/20"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-4">currently</h3>
                <ul className="space-y-2 text-white/75">
                  <li>• building cool web apps</li>
                  <li>• learning new frameworks</li>
                  <li>• keeping my github private like a hermit</li>
                </ul>
              </div>

              <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-4">discord status</h3>
                <DiscordStatus
                  user={data?.data}
                  error={data?.success === false}
                />
              </div>

              <div className="flex flex-wrap gap-4">
                <Button variant="outline" size="sm" asChild>
                  <a
                    href="https://github.com/occorune"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <SiGithub className="w-4 h-4 mr-2" />
                    GitHub
                  </a>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <a
                    href="https://x.com/occorocks"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <SiX className="w-4 h-4 mr-2" />
                    X/Twitter
                  </a>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <a
                    href="https://discord.com/users/@1383592267201384469"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <SiDiscord className="w-4 h-4 mr-2" />
                    Discord (User)
                  </a>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <a href="mailto:<EMAIL>">
                    <Mail className="w-4 h-4 mr-2" />
                    Email
                  </a>
                </Button>
              </div>
            </div>
          </div>

          <div className="mt-16 text-center">
            <p className="text-white/50 text-sm">
              thanks for stopping by! feel free to reach out for a website,
              edit, or something else. just let me know what you are needing and
              remember i don't always work for free. i may be asleep so give me a few hours as i sleep whenever and it is currently {now} in my timezone.
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
