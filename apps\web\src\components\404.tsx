import { <PERSON>, useRouter } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export function NotFound() {
    const router = useRouter();

    return (
        <div className="flex flex-col items-center justify-center h-screen">
            <img src="/404.svg" alt="404 illustration" className="w-96 h-96 mb-8" />
            <div className="relative text-center z-[1]">
                <h1 className="mt-4 text-balance text-5xl font-semibold tracking-tight text-primary sm:text-7xl">
                    Page not found
                </h1>
                <p className="mt-6 text-pretty text-lg font-medium text-muted-foreground sm:text-xl/8">
                    Lost, this page is. In another system, it may be.
                </p>
                <div className="mt-10 flex flex-col sm:flex-row sm:items-center sm:justify-center gap-y-3 gap-x-6">
                    <Button
                        variant="secondary"
                        onClick={() => router.history.back()}
                        className="group"
                    >
                        <ArrowLeft
                            className="me-2 ms-0 opacity-60 transition-transform group-hover:-translate-x-0.5"
                            size={16}
                            strokeWidth={2}
                            aria-hidden="true"
                        />
                        Go back
                    </Button>
                    <Button className="-order-1 sm:order-none" asChild>
                        <Link to="/">Take me home</Link>
                    </Button>
                </div>
            </div>
        </div>
    );
}
