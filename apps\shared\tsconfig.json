{
	"compilerOptions": {
		// Environment setup
		"lib": ["ESNext"],
		"target": "ESNext",
		"module": "ESNext",
		"moduleResolution": "bundler",

		// Output configuration
		"declaration": true,
		"declarationMap": true,
		"outDir": "./dist",
		"noEmit": false,
		"composite": true,

		// Type checking
		"strict": true,
		"skipLibCheck": true,

		// Additional checks
		"noFallthroughCasesInSwitch": true,
		"noUncheckedIndexedAccess": true,

		// Module resolution
		"allowSyntheticDefaultImports": true,
		"esModuleInterop": true,
		"forceConsistentCasingInFileNames": true
	},
	"include": ["src/**/*"],
	"exclude": ["node_modules", "**/*.test.ts", "dist"]
}
