{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "bun run --hot src/index.ts", "start": "bun run dist/index.js", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:seed": "bun run src/db/seed.ts"}, "dependencies": {"dotenv": "^16.4.7", "zod": "^4.0.2", "@orpc/server": "^1.5.0", "@orpc/client": "^1.5.0", "hono": "^4.8.2", "drizzle-orm": "^0.44.2", "pg": "^8.14.1", "better-auth": "^1.3.0"}, "devDependencies": {"tsdown": "^0.12.9", "typescript": "^5.8.2", "@types/bun": "^1.2.6", "drizzle-kit": "^0.31.2", "@types/pg": "^8.11.11"}}